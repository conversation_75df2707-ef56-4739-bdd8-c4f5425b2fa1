Dapp/src/main/java/com/example/castapp/viewmodel/ReceiverViewModel.kt?app/src/main/java/com/example/castapp/audio/AudioRtpReceiver.ktNapp/src/main/java/com/example/castapp/ui/fragment/RemoteReceiverTabFragment.ktBapp/src/main/java/com/example/castapp/manager/MicrophoneManager.kt?app/src/main/java/com/example/castapp/audio/AudioSyncManager.ktEapp/src/main/java/com/example/castapp/database/dao/WindowLayoutDao.kt;app/src/main/java/com/example/castapp/codec/VideoDecoder.ktFapp/src/main/java/com/example/castapp/service/RemoteReceiverService.kt>app/src/main/java/com/example/castapp/audio/AudioBufferPool.kt;app/src/main/java/com/example/castapp/ui/StopwatchWindow.ktLapp/src/main/java/com/example/castapp/ui/windowsettings/TransformRenderer.ktPapp/src/main/java/com/example/castapp/ui/dialog/AddRemoteReceiverDeviceDialog.ktKapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktKapp/src/main/java/com/example/castapp/database/entity/WindowLayoutEntity.ktFapp/src/main/java/com/example/castapp/ui/dialog/WindowManagerDialog.ktGapp/src/main/java/com/example/castapp/manager/MediaProjectionManager.kt6app/src/main/java/com/example/castapp/rtp/RtpPacket.ktPapp/src/main/java/com/example/castapp/ui/helper/LayoutItemTouchHelperCallback.ktFapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.ktNapp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktKapp/src/main/java/com/example/castapp/ui/dialog/RemoteLayerManagerDialog.kt?app/src/main/java/com/example/castapp/utils/MediaFileManager.kt=app/src/main/java/com/example/castapp/network/NetworkUtils.kt6app/src/main/java/com/example/castapp/rtp/RtpSender.ktCapp/src/main/java/com/example/castapp/ui/view/GestureOverlayView.ktBapp/src/main/java/com/example/castapp/websocket/WebSocketServer.kt<app/src/main/java/com/example/castapp/utils/MemoryMonitor.ktIapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.kt9app/src/main/java/com/example/castapp/utils/ColorUtils.ktLapp/src/main/java/com/example/castapp/ui/dialog/LineSpacingSettingsDialog.ktCapp/src/main/java/com/example/castapp/network/SmartBufferManager.ktQapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktBapp/src/main/java/com/example/castapp/utils/PeriodicCleanupTask.ktRapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDialogModule.ktLapp/src/main/java/com/example/castapp/ui/fragment/RemoteSenderTabFragment.ktFapp/src/main/java/com/example/castapp/manager/AudioReceivingManager.ktMapp/src/main/java/com/example/castapp/ui/adapter/CustomColorPaletteAdapter.ktIapp/src/main/java/com/example/castapp/database/converter/DateConverter.ktAapp/src/main/java/com/example/castapp/websocket/ControlMessage.ktGapp/src/main/java/com/example/castapp/ui/adapter/LayerManagerAdapter.kt=app/src/main/java/com/example/castapp/audio/AudioRtpSender.ktGapp/src/main/java/com/example/castapp/ui/dialog/FontFilePickerDialog.ktUapp/src/main/java/com/example/castapp/manager/windowsettings/WindowOperationModule.kt\app/src/main/java/com/example/castapp/ui/windowsettings/interfaces/TransformStateListener.ktPapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDataModule.ktBapp/src/main/java/com/example/castapp/utils/NotificationManager.ktWapp/src/main/java/com/example/castapp/ui/windowsettings/interfaces/CropStateListener.kt=app/src/main/java/com/example/castapp/manager/StateManager.ktCapp/src/main/java/com/example/castapp/ui/dialog/SaveLayoutDialog.ktDapp/src/main/java/com/example/castapp/ui/dialog/SaveOptionsDialog.ktCapp/src/main/java/com/example/castapp/ui/dialog/EditLayoutDialog.ktRapp/src/main/java/com/example/castapp/ui/windowsettings/RemoteTextWindowManager.kt>app/src/main/java/com/example/castapp/ui/view/TextEditPanel.kt9app/src/main/java/com/example/castapp/model/Connection.kt=app/src/main/java/com/example/castapp/model/CastWindowInfo.ktFapp/src/main/java/com/example/castapp/manager/RemoteWindowInfoCache.ktEapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktDapp/src/main/java/com/example/castapp/utils/FontSizePresetManager.ktIapp/src/main/java/com/example/castapp/ui/windowsettings/SurfaceManager.ktNapp/src/main/java/com/example/castapp/ui/windowsettings/MediaSurfaceManager.ktCapp/src/main/java/com/example/castapp/rtp/MultiConnectionManager.ktEapp/src/main/java/com/example/castapp/ui/dialog/FontSettingsDialog.kt:app/src/main/java/com/example/castapp/network/UdpSender.ktAapp/src/main/java/com/example/castapp/ui/dialog/NoteEditDialog.ktOapp/src/main/java/com/example/castapp/ui/dialog/EditRemoteSenderDeviceDialog.kt>app/src/main/java/com/example/castapp/manager/LayoutManager.ktLapp/src/main/java/com/example/castapp/ui/windowsettings/ScreenshotManager.ktFapp/src/main/java/com/example/castapp/ui/view/PrecisionControlPanel.ktDapp/src/main/java/com/example/castapp/ui/dialog/ColorPickerDialog.kt@app/src/main/java/com/example/castapp/utils/TextFormatManager.ktIapp/src/main/java/com/example/castapp/ui/adapter/RemoteTabPagerAdapter.kt@app/src/main/java/com/example/castapp/utils/FontPresetManager.ktIapp/src/main/java/com/example/castapp/service/FloatingStopwatchService.ktEapp/src/main/java/com/example/castapp/model/RemoteSenderConnection.ktQapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.kt9app/src/main/java/com/example/castapp/utils/StrokeSpan.ktGapp/src/main/java/com/example/castapp/utils/LineSpacingPresetManager.ktOapp/src/main/java/com/example/castapp/ui/adapter/RemoteReceiverDeviceAdapter.ktRapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktBapp/src/main/java/com/example/castapp/websocket/WebSocketClient.kt5app/src/main/java/com/example/castapp/utils/AppLog.kt>app/src/main/java/com/example/castapp/utils/ResourceManager.ktTapp/src/main/java/com/example/castapp/manager/windowsettings/WindowCreationModule.ktBapp/src/main/java/com/example/castapp/ui/ReceiverDialogFragment.ktAapp/src/main/java/com/example/castapp/manager/WebSocketManager.ktAapp/src/main/java/com/example/castapp/database/CastAppDatabase.kt=app/src/main/java/com/example/castapp/service/AudioService.kt;app/src/main/java/com/example/castapp/codec/VideoEncoder.ktIapp/src/main/java/com/example/castapp/ui/dialog/AddMediaDialogFragment.ktKapp/src/main/java/com/example/castapp/remote/RemoteSenderWebSocketClient.ktOapp/src/main/java/com/example/castapp/database/entity/WindowLayoutItemEntity.ktLapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktDapp/src/main/java/com/example/castapp/ui/view/ResizableBorderView.ktFapp/src/main/java/com/example/castapp/manager/FloatingWindowManager.ktUapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLifecycleModule.ktMapp/src/main/java/com/example/castapp/ui/adapter/RemoteSenderDeviceAdapter.kt@app/src/main/java/com/example/castapp/ui/view/CropOverlayView.ktGapp/src/main/java/com/example/castapp/model/RemoteReceiverConnection.ktEapp/src/main/java/com/example/castapp/ui/adapter/LayoutListAdapter.ktFapp/src/main/java/com/example/castapp/manager/RemoteReceiverManager.kt:app/src/main/java/com/example/castapp/utils/DeviceUtils.ktKapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktIapp/src/main/java/com/example/castapp/ui/dialog/FontSizeSettingsDialog.ktKapp/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.ktPapp/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.kt?app/src/main/java/com/example/castapp/ui/view/TextWindowView.ktHapp/src/main/java/com/example/castapp/manager/MessageReceivingManager.ktEapp/src/main/java/com/example/castapp/ui/adapter/ConnectionAdapter.ktIapp/src/main/java/com/example/castapp/utils/LetterSpacingPresetManager.kt@app/src/main/java/com/example/castapp/viewmodel/MainViewModel.kt8app/src/main/java/com/example/castapp/ui/MainActivity.ktCapp/src/main/java/com/example/castapp/manager/MultiCameraManager.kt:app/src/main/java/com/example/castapp/utils/NoteManager.kt@app/src/main/java/com/example/castapp/utils/LetterSpacingSpan.ktMapp/src/main/java/com/example/castapp/manager/PrecisionControlPanelManager.ktDapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.kt<app/src/main/java/com/example/castapp/network/UdpReceiver.ktBapp/src/main/java/com/example/castapp/audio/AudioCaptureManager.kt@app/src/main/java/com/example/castapp/manager/HideShowManager.ktDapp/src/main/java/com/example/castapp/manager/RemoteSenderManager.ktBapp/src/main/java/com/example/castapp/utils/ColorPaletteManager.ktEapp/src/main/java/com/example/castapp/ui/dialog/LayerManagerDialog.kt>app/src/main/java/com/example/castapp/utils/LineSpacingSpan.ktGapp/src/main/java/com/example/castapp/ui/adapter/LayoutDetailAdapter.ktPapp/src/main/java/com/example/castapp/ui/windowsettings/WindowPositionManager.ktNapp/src/main/java/com/example/castapp/ui/dialog/AddRemoteSenderDeviceDialog.kt8app/src/main/java/com/example/castapp/rtp/RtpReceiver.ktVapp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverSettingsControlDialog.ktNapp/src/main/java/com/example/castapp/ui/dialog/LetterSpacingSettingsDialog.ktFapp/src/main/java/com/example/castapp/ui/windowsettings/CropManager.ktAapp/src/main/java/com/example/castapp/ui/dialog/DirectorDialog.kt8app/src/main/java/com/example/castapp/rtp/PayloadView.ktAapp/src/main/java/com/example/castapp/service/ReceivingService.ktBapp/src/main/java/com/example/castapp/manager/ResolutionManager.kt?app/src/main/java/com/example/castapp/model/WindowUpdateMode.ktHapp/src/main/java/com/example/castapp/manager/RemoteConnectionManager.ktMapp/src/main/java/com/example/castapp/ui/dialog/RemoteControlManagerDialog.ktFapp/src/main/java/com/example/castapp/model/WindowVisualizationData.kt;app/src/main/java/com/example/castapp/audio/AudioDecoder.kt9app/src/main/java/com/example/castapp/utils/ToastUtils.kt:app/src/main/java/com/example/castapp/audio/AudioPlayer.ktAapp/src/main/java/com/example/castapp/model/RemoteWindowConfig.kt;app/src/main/java/com/example/castapp/audio/AudioEncoder.ktGapp/src/main/java/com/example/castapp/ui/adapter/RemoteSenderAdapter.ktFapp/src/main/java/com/example/castapp/manager/VideoReceivingManager.kt@app/src/main/java/com/example/castapp/ui/SenderDialogFragment.ktBapp/src/main/java/com/example/castapp/manager/PermissionManager.kt>app/src/main/java/com/example/castapp/utils/TextSizeManager.kt?app/src/main/java/com/example/castapp/service/CastingService.ktBapp/src/main/java/com/example/castapp/remote/RemoteSenderServer.ktLapp/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.ktHapp/src/main/java/com/example/castapp/ui/adapter/WindowManagerAdapter.ktQapp/src/main/java/com/example/castapp/ui/dialog/EditRemoteReceiverDeviceDialog.ktBapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktLapp/src/main/java/com/example/castapp/ui/dialog/RemoteSenderControlDialog.ktBapp/src/main/java/com/example/castapp/test/WindowSyncTestHelper.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               